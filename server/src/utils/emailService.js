import nodemailer from 'nodemailer';
import { EmailConfig } from '../models/EmailConfig.js';

/**
 * 邮件服务工具类
 */
export class EmailService {
  constructor() {
    this.transporter = null;
    this.config = null;
  }

  /**
   * 初始化邮件传输器
   * @returns {Promise<boolean>} 初始化是否成功
   */
  async initialize() {
    try {
      // 获取激活的邮箱配置
      this.config = await EmailConfig.getActiveConfig();
      
      if (!this.config) {
        console.warn('未找到激活的邮箱配置');
        return false;
      }

      // 创建传输器
      this.transporter = nodemailer.createTransporter({
        host: this.config.smtp_host,
        port: this.config.smtp_port,
        secure: this.config.smtp_secure,
        auth: {
          user: this.config.smtp_user,
          pass: this.config.smtp_pass
        },
        // 163邮箱特殊配置
        tls: {
          rejectUnauthorized: false
        }
      });

      // 验证连接
      await this.transporter.verify();
      console.log('邮件服务初始化成功');
      return true;
    } catch (error) {
      console.error('邮件服务初始化失败:', error);
      this.transporter = null;
      this.config = null;
      return false;
    }
  }

  /**
   * 发送邮件
   * @param {Object} mailOptions - 邮件选项
   * @returns {Promise<Object>} 发送结果
   */
  async sendMail(mailOptions) {
    try {
      // 确保传输器已初始化
      if (!this.transporter) {
        const initialized = await this.initialize();
        if (!initialized) {
          throw new Error('邮件服务未初始化');
        }
      }

      // 设置默认发件人信息
      const options = {
        from: `"${this.config.from_name}" <${this.config.from_email}>`,
        ...mailOptions
      };

      const result = await this.transporter.sendMail(options);
      console.log('邮件发送成功:', result.messageId);
      return result;
    } catch (error) {
      console.error('邮件发送失败:', error);
      throw error;
    }
  }

  /**
   * 发送验证码邮件
   * @param {string} email - 收件人邮箱
   * @param {string} code - 验证码
   * @param {string} type - 验证码类型
   * @returns {Promise<Object>} 发送结果
   */
  async sendVerificationCode(email, code, type = 'register') {
    const templates = {
      register: {
        subject: '【走失宠物协寻平台】邮箱验证码',
        html: this.getRegisterTemplate(code)
      },
      reset_password: {
        subject: '【走失宠物协寻平台】密码重置验证码',
        html: this.getResetPasswordTemplate(code)
      }
    };

    const template = templates[type];
    if (!template) {
      throw new Error('不支持的验证码类型');
    }

    return await this.sendMail({
      to: email,
      subject: template.subject,
      html: template.html
    });
  }

  /**
   * 获取注册验证码邮件模板
   * @param {string} code - 验证码
   * @returns {string} HTML模板
   */
  getRegisterTemplate(code) {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>邮箱验证码</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #4F46E5; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
          .code { background: #fff; border: 2px dashed #4F46E5; padding: 20px; text-align: center; margin: 20px 0; border-radius: 8px; }
          .code-number { font-size: 32px; font-weight: bold; color: #4F46E5; letter-spacing: 5px; }
          .footer { text-align: center; margin-top: 20px; color: #666; font-size: 14px; }
          .warning { background: #FEF3C7; border-left: 4px solid #F59E0B; padding: 15px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>走失宠物协寻平台</h1>
            <p>邮箱验证码</p>
          </div>
          <div class="content">
            <h2>您好！</h2>
            <p>感谢您注册走失宠物协寻平台。为了确保您的账户安全，请使用以下验证码完成邮箱验证：</p>
            
            <div class="code">
              <div class="code-number">${code}</div>
              <p>验证码有效期：10分钟</p>
            </div>
            
            <div class="warning">
              <strong>安全提醒：</strong>
              <ul>
                <li>请勿将验证码告诉他人</li>
                <li>验证码仅用于本次注册，请在10分钟内使用</li>
                <li>如果您没有进行注册操作，请忽略此邮件</li>
              </ul>
            </div>
            
            <p>如果您有任何问题，请联系我们的客服团队。</p>
            <p>祝您使用愉快！</p>
          </div>
          <div class="footer">
            <p>此邮件由系统自动发送，请勿回复</p>
            <p>© 2024 走失宠物协寻平台 版权所有</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * 获取密码重置验证码邮件模板
   * @param {string} code - 验证码
   * @returns {string} HTML模板
   */
  getResetPasswordTemplate(code) {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>密码重置验证码</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #DC2626; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
          .code { background: #fff; border: 2px dashed #DC2626; padding: 20px; text-align: center; margin: 20px 0; border-radius: 8px; }
          .code-number { font-size: 32px; font-weight: bold; color: #DC2626; letter-spacing: 5px; }
          .footer { text-align: center; margin-top: 20px; color: #666; font-size: 14px; }
          .warning { background: #FEE2E2; border-left: 4px solid #DC2626; padding: 15px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>走失宠物协寻平台</h1>
            <p>密码重置验证码</p>
          </div>
          <div class="content">
            <h2>密码重置请求</h2>
            <p>我们收到了您的密码重置请求。请使用以下验证码完成密码重置：</p>
            
            <div class="code">
              <div class="code-number">${code}</div>
              <p>验证码有效期：10分钟</p>
            </div>
            
            <div class="warning">
              <strong>安全提醒：</strong>
              <ul>
                <li>如果您没有申请密码重置，请立即联系我们</li>
                <li>请勿将验证码告诉他人</li>
                <li>验证码仅用于本次密码重置，请在10分钟内使用</li>
              </ul>
            </div>
            
            <p>如果您有任何问题，请联系我们的客服团队。</p>
          </div>
          <div class="footer">
            <p>此邮件由系统自动发送，请勿回复</p>
            <p>© 2024 走失宠物协寻平台 版权所有</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  /**
   * 测试邮件配置
   * @param {Object} configData - 邮箱配置数据
   * @returns {Promise<boolean>} 测试是否成功
   */
  static async testConfig(configData) {
    try {
      const testTransporter = nodemailer.createTransporter({
        host: configData.smtp_host,
        port: configData.smtp_port,
        secure: configData.smtp_secure,
        auth: {
          user: configData.smtp_user,
          pass: configData.smtp_pass
        },
        tls: {
          rejectUnauthorized: false
        }
      });

      await testTransporter.verify();
      return true;
    } catch (error) {
      console.error('邮箱配置测试失败:', error);
      return false;
    }
  }

  /**
   * 发送测试邮件
   * @param {string} email - 收件人邮箱
   * @returns {Promise<Object>} 发送结果
   */
  async sendTestEmail(email) {
    const html = `
      <h2>邮箱配置测试</h2>
      <p>这是一封测试邮件，用于验证邮箱配置是否正确。</p>
      <p>如果您收到此邮件，说明邮箱配置已成功！</p>
      <p>发送时间：${new Date().toLocaleString('zh-CN')}</p>
    `;

    return await this.sendMail({
      to: email,
      subject: '【走失宠物协寻平台】邮箱配置测试',
      html
    });
  }
}

// 创建全局邮件服务实例
export const emailService = new EmailService();
